{% extends "base.html" %}

{% block title %}首页 - 考研英语长难句智能精练系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 欢迎区域 -->
    <div class="col-12 mb-4">
        <div class="jumbotron bg-gradient-primary text-white p-5 rounded">
            <h1 class="display-4">
                <i class="fas fa-rocket me-3"></i>
                考研英语长难句智能精练系统
            </h1>
            <p class="lead">基于AI技术的长难句学习平台，助你攻克考研英语阅读难关</p>
            <hr class="my-4">
            <p>每日精练 • 深度解析 • 智能巩固 • 能力追踪</p>
            <a class="btn btn-light btn-lg" href="{{ url_for('practice.daily_practice') }}" role="button">
                <i class="fas fa-play me-2"></i>开始今日练习
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- 学习统计卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-book-open fa-3x text-primary mb-3"></i>
                <h5 class="card-title">已练习句子</h5>
                <h2 class="text-primary">{{ user_stats.total_sentences }}</h2>
                <p class="card-text text-muted">累计练习</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                <h5 class="card-title">平均得分</h5>
                <h2 class="text-success">{{ user_stats.average_score }}%</h2>
                <p class="card-text text-muted">综合表现</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-clock fa-3x text-info mb-3"></i>
                <h5 class="card-title">学习时长</h5>
                <h2 class="text-info">{{ (user_stats.total_time / 60) | round(1) }}</h2>
                <p class="card-text text-muted">分钟</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-target fa-3x text-warning mb-3"></i>
                <h5 class="card-title">理解准确率</h5>
                <h2 class="text-warning">{{ user_stats.comprehension_accuracy }}%</h2>
                <p class="card-text text-muted">理解验证</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 功能特色 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>核心功能
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <i class="fas fa-puzzle-piece fa-2x text-primary mb-2"></i>
                            <h6>结构拆解</h6>
                            <small class="text-muted">识别句子主干和修饰成分</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <i class="fas fa-language fa-2x text-success mb-2"></i>
                            <h6>翻译练习</h6>
                            <small class="text-muted">提升英译中能力</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <i class="fas fa-lightbulb fa-2x text-warning mb-2"></i>
                            <h6>理解验证</h6>
                            <small class="text-muted">检验深层理解能力</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <i class="fas fa-microscope fa-2x text-info mb-2"></i>
                            <h6>深度解析</h6>
                            <small class="text-muted">语法词汇全面解析</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速开始 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>快速开始
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('practice.daily_practice') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-dumbbell me-2"></i>每日练习
                    </a>
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-2"></i>查看学习统计
                    </a>
                    <a href="{{ url_for('analysis.analysis_home') }}" class="btn btn-outline-success">
                        <i class="fas fa-search me-2"></i>句子解析库
                    </a>
                </div>
                
                {% if user_stats.weak_points %}
                <div class="mt-3">
                    <h6 class="text-muted">需要加强的方面：</h6>
                    {% for point, count in user_stats.weak_points[:3] %}
                    <span class="badge bg-warning me-1">{{ point }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 学习建议 -->
{% if user_stats.total_sentences > 0 %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <h5 class="alert-heading">
                <i class="fas fa-lightbulb me-2"></i>学习建议
            </h5>
            {% if user_stats.average_score < 60 %}
            <p>建议加强基础练习，重点关注句子结构分析和词汇理解。</p>
            {% elif user_stats.average_score < 80 %}
            <p>表现不错！建议继续练习，特别关注理解验证部分的准确性。</p>
            {% else %}
            <p>优秀！可以尝试更高难度的句子，或者帮助其他同学一起进步。</p>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
{% endblock %}
