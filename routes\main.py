from flask import Blueprint, render_template, request, jsonify, session
from services.data_service import DataService
from models.user_progress import UserProgress
import uuid

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """首页"""
    # 确保用户有session_id
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    # 获取用户统计信息
    user_stats = UserProgress.get_user_stats(session['session_id'])
    
    return render_template('index.html', user_stats=user_stats)

@main_bp.route('/dashboard')
def dashboard():
    """学习仪表板"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    # 获取详细统计信息
    user_stats = UserProgress.get_user_stats(session['session_id'])
    
    # 获取最近的练习记录
    recent_records = UserProgress.query.filter_by(
        session_id=session['session_id']
    ).order_by(UserProgress.created_at.desc()).limit(10).all()
    
    return render_template('dashboard.html', 
                         user_stats=user_stats,
                         recent_records=[r.to_dict() for r in recent_records])

@main_bp.route('/api/sentences')
def get_sentences():
    """获取句子列表API"""
    data_service = DataService()
    
    # 获取查询参数
    difficulty = request.args.get('difficulty', type=int)
    topic = request.args.get('topic')
    grammar_tag = request.args.get('grammar_tag')
    limit = request.args.get('limit', 10, type=int)
    
    sentences = data_service.get_sentences_by_criteria(
        difficulty=difficulty,
        topic=topic, 
        grammar_tag=grammar_tag,
        limit=limit
    )
    
    return jsonify({
        'success': True,
        'data': [s.to_dict() for s in sentences],
        'count': len(sentences)
    })

@main_bp.route('/api/daily-sentences')
def get_daily_sentences():
    """获取每日练习句子API"""
    data_service = DataService()
    count = request.args.get('count', 3, type=int)
    
    sentences = data_service.get_daily_practice_sentences(count=count)
    
    return jsonify({
        'success': True,
        'data': [s.to_dict() for s in sentences],
        'count': len(sentences)
    })

@main_bp.route('/api/user-stats')
def get_user_stats():
    """获取用户统计信息API"""
    if 'session_id' not in session:
        return jsonify({
            'success': False,
            'message': 'No session found'
        })
    
    stats = UserProgress.get_user_stats(session['session_id'])
    
    return jsonify({
        'success': True,
        'data': stats
    })
