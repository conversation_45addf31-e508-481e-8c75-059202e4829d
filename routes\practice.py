from flask import Blueprint, render_template, request, jsonify, session
from models.sentence import Sentence
from models.user_progress import UserProgress
from database import db
from services.practice_service import PracticeService
from datetime import datetime
import uuid

practice_bp = Blueprint('practice', __name__)

@practice_bp.route('/')
def practice_home():
    """练习首页"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    return render_template('practice/index.html')

@practice_bp.route('/daily')
def daily_practice():
    """每日练习页面"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    # 获取今日练习句子
    sentences = Sentence.get_daily_sentences(count=3)
    
    return render_template('practice/daily.html', sentences=sentences)

@practice_bp.route('/sentence/<int:sentence_id>')
def practice_sentence(sentence_id):
    """单句练习页面"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    sentence = Sentence.query.get_or_404(sentence_id)
    
    return render_template('practice/sentence.html', sentence=sentence)

@practice_bp.route('/api/start-practice', methods=['POST'])
def start_practice():
    """开始练习API"""
    if 'session_id' not in session:
        return jsonify({'success': False, 'message': 'No session found'})
    
    data = request.get_json()
    sentence_id = data.get('sentence_id')
    
    if not sentence_id:
        return jsonify({'success': False, 'message': 'Sentence ID required'})
    
    sentence = Sentence.query.get(sentence_id)
    if not sentence:
        return jsonify({'success': False, 'message': 'Sentence not found'})
    
    # 创建练习记录
    progress = UserProgress(
        session_id=session['session_id'],
        sentence_id=sentence_id,
        start_time=datetime.utcnow()
    )
    
    db.session.add(progress)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'progress_id': progress.id,
        'sentence': sentence.to_dict()
    })

@practice_bp.route('/api/submit-structure', methods=['POST'])
def submit_structure():
    """提交结构拆解答案"""
    data = request.get_json()
    progress_id = data.get('progress_id')
    user_structure = data.get('user_structure')
    
    progress = UserProgress.query.get(progress_id)
    if not progress:
        return jsonify({'success': False, 'message': 'Progress not found'})
    
    # 评分逻辑（简化版本）
    practice_service = PracticeService()
    score = practice_service.evaluate_structure(progress.sentence, user_structure)
    
    # 更新记录
    progress.user_structure = user_structure
    progress.structure_score = score
    db.session.commit()
    
    return jsonify({
        'success': True,
        'score': score,
        'feedback': practice_service.get_structure_feedback(progress.sentence, user_structure, score)
    })

@practice_bp.route('/api/submit-translation', methods=['POST'])
def submit_translation():
    """提交翻译答案"""
    data = request.get_json()
    progress_id = data.get('progress_id')
    user_translation = data.get('user_translation')
    
    progress = UserProgress.query.get(progress_id)
    if not progress:
        return jsonify({'success': False, 'message': 'Progress not found'})
    
    # 评分逻辑（简化版本）
    practice_service = PracticeService()
    score = practice_service.evaluate_translation(progress.sentence, user_translation)
    
    # 更新记录
    progress.user_translation = user_translation
    progress.translation_score = score
    db.session.commit()
    
    return jsonify({
        'success': True,
        'score': score,
        'reference_translation': progress.sentence.translation,
        'feedback': practice_service.get_translation_feedback(progress.sentence, user_translation, score)
    })

@practice_bp.route('/api/submit-comprehension', methods=['POST'])
def submit_comprehension():
    """提交理解验证答案"""
    data = request.get_json()
    progress_id = data.get('progress_id')
    user_answer = data.get('user_answer')
    
    progress = UserProgress.query.get(progress_id)
    if not progress:
        return jsonify({'success': False, 'message': 'Progress not found'})
    
    # 生成理解题并评分（简化版本）
    practice_service = PracticeService()
    question_data = practice_service.generate_comprehension_question(progress.sentence)
    score = practice_service.evaluate_comprehension(question_data, user_answer)
    
    # 更新记录
    progress.user_comprehension = user_answer
    progress.comprehension_score = score
    progress.end_time = datetime.utcnow()
    
    # 计算总分和时长
    if progress.start_time and progress.end_time:
        progress.duration = int((progress.end_time - progress.start_time).total_seconds())
    
    progress.total_score = (
        (progress.structure_score or 0) + 
        (progress.translation_score or 0) + 
        (progress.comprehension_score or 0)
    ) / 3
    
    # 分析错误类型和薄弱点
    error_analysis = practice_service.analyze_errors(progress)
    progress.error_types = error_analysis['error_types']
    progress.weak_points = error_analysis['weak_points']
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'score': score,
        'total_score': progress.total_score,
        'question': question_data,
        'correct_answer': question_data['correct_answer'],
        'explanation': question_data['explanation'],
        'error_analysis': error_analysis
    })
