from datetime import datetime
import json
from database import db

class Sentence(db.Model):
    """长难句模型"""
    __tablename__ = 'sentences'

    id = db.Column(db.Integer, primary_key=True)
    
    # 基本信息
    content = db.Column(db.Text, nullable=False)  # 句子内容
    source = db.Column(db.String(100))  # 来源（如：2020年Text1）
    topic = db.Column(db.String(50))  # 话题分类
    
    # 难度指标
    word_count = db.Column(db.Integer)  # 词数
    difficulty_level = db.Column(db.Integer, default=1)  # 难度等级 1-5
    flesch_score = db.Column(db.Float)  # Flesch可读性分数
    
    # 语法特征
    grammar_tags = db.Column(db.Text)  # JSON格式存储语法标签
    key_structures = db.Column(db.Text)  # 关键语法结构
    
    # 词汇信息
    key_vocabulary = db.Column(db.Text)  # JSON格式存储重点词汇
    
    # 解析信息
    main_clause = db.Column(db.Text)  # 主句
    subordinate_clauses = db.Column(db.Text)  # JSON格式存储从句
    translation = db.Column(db.Text)  # 参考翻译
    analysis = db.Column(db.Text)  # 详细解析
    
    # 元数据
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def __repr__(self):
        return f'<Sentence {self.id}: {self.content[:50]}...>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'content': self.content,
            'source': self.source,
            'topic': self.topic,
            'word_count': self.word_count,
            'difficulty_level': self.difficulty_level,
            'flesch_score': self.flesch_score,
            'grammar_tags': json.loads(self.grammar_tags) if self.grammar_tags else [],
            'key_structures': self.key_structures,
            'key_vocabulary': json.loads(self.key_vocabulary) if self.key_vocabulary else [],
            'main_clause': self.main_clause,
            'subordinate_clauses': json.loads(self.subordinate_clauses) if self.subordinate_clauses else [],
            'translation': self.translation,
            'analysis': self.analysis,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def get_daily_sentences(cls, count=3, difficulty_range=(1, 5)):
        """获取每日练习句子"""
        return cls.query.filter(
            cls.is_active == True,
            cls.difficulty_level.between(difficulty_range[0], difficulty_range[1])
        ).order_by(db.func.random()).limit(count).all()
    
    @classmethod
    def get_by_difficulty(cls, level):
        """根据难度获取句子"""
        return cls.query.filter(
            cls.is_active == True,
            cls.difficulty_level == level
        ).all()
    
    @classmethod
    def search_by_grammar(cls, grammar_tag):
        """根据语法标签搜索句子"""
        return cls.query.filter(
            cls.is_active == True,
            cls.grammar_tags.contains(grammar_tag)
        ).all()
