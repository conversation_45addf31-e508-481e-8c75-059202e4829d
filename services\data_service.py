import json
import os
from models.sentence import Sentence
from database import db

class DataService:
    """数据服务类，负责数据的初始化和管理"""
    
    def __init__(self):
        self.data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
    
    def initialize_sample_data(self):
        """初始化示例数据"""
        # 检查是否已有数据
        if Sentence.query.count() > 0:
            return
        
        # 加载JSON数据
        sentences_file = os.path.join(self.data_path, 'sentences.json')
        if not os.path.exists(sentences_file):
            return
        
        with open(sentences_file, 'r', encoding='utf-8') as f:
            sentences_data = json.load(f)
        
        # 插入数据
        for data in sentences_data:
            sentence = Sentence(
                content=data['content'],
                source=data['source'],
                topic=data['topic'],
                word_count=data['word_count'],
                difficulty_level=data['difficulty_level'],
                flesch_score=data['flesch_score'],
                grammar_tags=json.dumps(data['grammar_tags'], ensure_ascii=False),
                key_structures=data['key_structures'],
                key_vocabulary=json.dumps(data['key_vocabulary'], ensure_ascii=False),
                main_clause=data['main_clause'],
                subordinate_clauses=json.dumps(data['subordinate_clauses'], ensure_ascii=False),
                translation=data['translation'],
                analysis=data['analysis']
            )
            db.session.add(sentence)
        
        try:
            db.session.commit()
            print(f"Successfully initialized {len(sentences_data)} sentences")
        except Exception as e:
            db.session.rollback()
            print(f"Error initializing data: {e}")
    
    def add_sentence(self, sentence_data):
        """添加新句子"""
        sentence = Sentence(
            content=sentence_data['content'],
            source=sentence_data.get('source'),
            topic=sentence_data.get('topic'),
            word_count=len(sentence_data['content'].split()),
            difficulty_level=sentence_data.get('difficulty_level', 1),
            flesch_score=sentence_data.get('flesch_score'),
            grammar_tags=json.dumps(sentence_data.get('grammar_tags', []), ensure_ascii=False),
            key_structures=sentence_data.get('key_structures'),
            key_vocabulary=json.dumps(sentence_data.get('key_vocabulary', []), ensure_ascii=False),
            main_clause=sentence_data.get('main_clause'),
            subordinate_clauses=json.dumps(sentence_data.get('subordinate_clauses', []), ensure_ascii=False),
            translation=sentence_data.get('translation'),
            analysis=sentence_data.get('analysis')
        )
        
        db.session.add(sentence)
        try:
            db.session.commit()
            return sentence
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_sentences_by_criteria(self, difficulty=None, topic=None, grammar_tag=None, limit=10):
        """根据条件获取句子"""
        query = Sentence.query.filter(Sentence.is_active == True)
        
        if difficulty:
            query = query.filter(Sentence.difficulty_level == difficulty)
        
        if topic:
            query = query.filter(Sentence.topic == topic)
        
        if grammar_tag:
            query = query.filter(Sentence.grammar_tags.contains(grammar_tag))
        
        return query.limit(limit).all()
    
    def get_daily_practice_sentences(self, count=3):
        """获取每日练习句子"""
        return Sentence.get_daily_sentences(count=count)
    
    def analyze_sentence_difficulty(self, content):
        """分析句子难度（简化版本）"""
        words = content.split()
        word_count = len(words)
        
        # 简单的难度评估
        if word_count < 15:
            difficulty = 1
        elif word_count < 25:
            difficulty = 2
        elif word_count < 35:
            difficulty = 3
        elif word_count < 45:
            difficulty = 4
        else:
            difficulty = 5
        
        # 检查复杂语法结构
        complex_indicators = [
            'which', 'that', 'who', 'whom', 'whose',  # 定语从句
            'although', 'though', 'despite', 'while',  # 让步状语
            'because', 'since', 'as', 'for',  # 原因状语
            'having', 'being', 'to be', 'to have'  # 非谓语动词
        ]
        
        complex_count = sum(1 for indicator in complex_indicators if indicator in content.lower())
        if complex_count >= 3:
            difficulty = min(5, difficulty + 1)
        
        return difficulty
