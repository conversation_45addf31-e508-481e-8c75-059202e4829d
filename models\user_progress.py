from datetime import datetime
import json
from database import db

class UserProgress(db.Model):
    """用户学习进度模型"""
    __tablename__ = 'user_progress'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 用户标识（简化版本，使用session_id）
    session_id = db.Column(db.String(100), nullable=False)
    
    # 练习记录
    sentence_id = db.Column(db.Integer, db.ForeignKey('sentences.id'), nullable=False)
    
    # 练习结果
    structure_score = db.Column(db.Float, default=0.0)  # 结构拆解得分
    translation_score = db.Column(db.Float, default=0.0)  # 翻译得分
    comprehension_score = db.Column(db.Float, default=0.0)  # 理解验证得分
    total_score = db.Column(db.Float, default=0.0)  # 总分
    
    # 用户答案
    user_structure = db.Column(db.Text)  # 用户的结构拆解
    user_translation = db.Column(db.Text)  # 用户翻译
    user_comprehension = db.Column(db.Text)  # 用户理解选择
    
    # 错误分析
    error_types = db.Column(db.Text)  # JSON格式存储错误类型
    weak_points = db.Column(db.Text)  # JSON格式存储薄弱点
    
    # 时间记录
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    duration = db.Column(db.Integer)  # 练习时长（秒）
    
    # 元数据
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    sentence = db.relationship('Sentence', backref='progress_records')
    
    def __repr__(self):
        return f'<UserProgress {self.id}: Session {self.session_id}, Sentence {self.sentence_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'sentence_id': self.sentence_id,
            'structure_score': self.structure_score,
            'translation_score': self.translation_score,
            'comprehension_score': self.comprehension_score,
            'total_score': self.total_score,
            'user_structure': self.user_structure,
            'user_translation': self.user_translation,
            'user_comprehension': self.user_comprehension,
            'error_types': json.loads(self.error_types) if self.error_types else [],
            'weak_points': json.loads(self.weak_points) if self.weak_points else [],
            'duration': self.duration,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def get_user_stats(cls, session_id):
        """获取用户统计信息"""
        records = cls.query.filter_by(session_id=session_id).all()
        
        if not records:
            return {
                'total_sentences': 0,
                'average_score': 0,
                'structure_accuracy': 0,
                'translation_accuracy': 0,
                'comprehension_accuracy': 0,
                'total_time': 0,
                'weak_points': []
            }
        
        total_sentences = len(records)
        avg_score = sum(r.total_score for r in records) / total_sentences
        structure_acc = sum(r.structure_score for r in records) / total_sentences
        translation_acc = sum(r.translation_score for r in records) / total_sentences
        comprehension_acc = sum(r.comprehension_score for r in records) / total_sentences
        total_time = sum(r.duration or 0 for r in records)
        
        # 收集薄弱点
        all_weak_points = []
        for record in records:
            if record.weak_points:
                all_weak_points.extend(json.loads(record.weak_points))
        
        # 统计薄弱点频率
        weak_point_counts = {}
        for point in all_weak_points:
            weak_point_counts[point] = weak_point_counts.get(point, 0) + 1
        
        return {
            'total_sentences': total_sentences,
            'average_score': round(avg_score, 2),
            'structure_accuracy': round(structure_acc, 2),
            'translation_accuracy': round(translation_acc, 2),
            'comprehension_accuracy': round(comprehension_acc, 2),
            'total_time': total_time,
            'weak_points': sorted(weak_point_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        }
