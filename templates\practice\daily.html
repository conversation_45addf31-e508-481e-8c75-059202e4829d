{% extends "base.html" %}

{% block title %}每日练习 - 考研英语长难句智能精练系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-dumbbell text-primary me-2"></i>
                每日练习
            </h2>
            <div class="text-muted">
                <i class="fas fa-calendar-day me-1"></i>
                {{ moment().format('YYYY年MM月DD日') }}
            </div>
        </div>
        <p class="text-muted">今日为您精选了3个长难句，让我们开始练习吧！</p>
    </div>
</div>

<div class="row">
    {% for sentence in sentences %}
    <div class="col-md-4 mb-4">
        <div class="card h-100 sentence-card" data-sentence-id="{{ sentence.id }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span class="badge bg-primary">难度 {{ sentence.difficulty_level }}/5</span>
                <small class="text-muted">{{ sentence.source }}</small>
            </div>
            <div class="card-body">
                <div class="sentence-preview mb-3">
                    <p class="card-text">{{ sentence.content[:100] }}{% if sentence.content|length > 100 %}...{% endif %}</p>
                </div>
                
                <div class="sentence-info">
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <small class="text-muted">词数</small>
                            <div class="fw-bold">{{ sentence.word_count }}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">话题</small>
                            <div class="fw-bold">{{ sentence.topic }}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">语法点</small>
                            <div class="fw-bold">{{ sentence.grammar_tags|length }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="grammar-tags mb-3">
                    {% for tag in sentence.grammar_tags %}
                    <span class="badge bg-secondary me-1">{{ tag }}</span>
                    {% endfor %}
                </div>
            </div>
            <div class="card-footer">
                <div class="d-grid">
                    <a href="{{ url_for('practice.practice_sentence', sentence_id=sentence.id) }}" 
                       class="btn btn-primary">
                        <i class="fas fa-play me-2"></i>开始练习
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 练习说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>练习说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-primary">
                            <i class="fas fa-puzzle-piece me-2"></i>第一步：结构拆解
                        </h6>
                        <p class="small">识别句子的主干结构，标注主语、谓语、宾语等成分，理清句子的层次关系。</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-success">
                            <i class="fas fa-language me-2"></i>第二步：翻译练习
                        </h6>
                        <p class="small">将英文句子翻译成通顺的中文，注意语言的准确性和流畅性。</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-warning">
                            <i class="fas fa-lightbulb me-2"></i>第三步：理解验证
                        </h6>
                        <p class="small">通过选择题验证对句子深层含义和逻辑关系的理解。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 学习提示 -->
<div class="row mt-3">
    <div class="col-12">
        <div class="alert alert-light">
            <h6 class="alert-heading">
                <i class="fas fa-lightbulb text-warning me-2"></i>学习提示
            </h6>
            <ul class="mb-0 small">
                <li>建议每天坚持练习，培养语感和分析能力</li>
                <li>遇到困难时，可以先查看句子解析再进行练习</li>
                <li>注意总结常见的语法结构和词汇搭配</li>
                <li>练习后及时查看反馈，针对薄弱点进行强化</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.sentence-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.sentence-card:hover {
    transform: translateY(-5px);
    border-color: #007bff;
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
}

.sentence-preview {
    min-height: 80px;
    display: flex;
    align-items: center;
}

.grammar-tags {
    min-height: 40px;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 添加卡片点击效果
    $('.sentence-card').click(function(e) {
        if (!$(e.target).is('a, button')) {
            const sentenceId = $(this).data('sentence-id');
            window.location.href = `/practice/sentence/${sentenceId}`;
        }
    });
    
    // 添加键盘快捷键
    $(document).keydown(function(e) {
        if (e.key >= '1' && e.key <= '3') {
            const index = parseInt(e.key) - 1;
            const cards = $('.sentence-card');
            if (cards[index]) {
                const sentenceId = $(cards[index]).data('sentence-id');
                window.location.href = `/practice/sentence/${sentenceId}`;
            }
        }
    });
});
</script>
{% endblock %}
