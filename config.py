import os
from datetime import timedelta

class Config:
    """应用配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///english_learning.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    
    # 应用配置
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 学习配置
    DAILY_SENTENCE_COUNT = 3  # 每日练习句子数量
    MIN_SENTENCE_LENGTH = 25  # 最小句子长度（词数）
    
    # AI分析配置
    ENABLE_NLP_ANALYSIS = True
    
class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
