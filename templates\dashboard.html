{% extends "base.html" %}

{% block title %}学习统计 - 考研英语长难句智能精练系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2>
            <i class="fas fa-chart-line text-primary me-2"></i>
            学习统计仪表板
        </h2>
        <p class="text-muted">全面了解您的学习进度和能力发展</p>
    </div>
</div>

<!-- 核心统计指标 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100 stats-card">
            <div class="card-body">
                <i class="fas fa-book-open fa-3x text-primary mb-3"></i>
                <div class="stats-number text-primary">{{ user_stats.total_sentences }}</div>
                <div class="stats-label">已练习句子</div>
                <small class="text-muted">累计完成</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100 stats-card">
            <div class="card-body">
                <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                <div class="stats-number text-warning">{{ user_stats.average_score }}%</div>
                <div class="stats-label">平均得分</div>
                <small class="text-muted">综合表现</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100 stats-card">
            <div class="card-body">
                <i class="fas fa-clock fa-3x text-info mb-3"></i>
                <div class="stats-number text-info">{{ (user_stats.total_time / 60) | round(1) }}</div>
                <div class="stats-label">学习时长</div>
                <small class="text-muted">分钟</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center h-100 stats-card">
            <div class="card-body">
                <i class="fas fa-fire fa-3x text-danger mb-3"></i>
                <div class="stats-number text-danger">7</div>
                <div class="stats-label">连续学习</div>
                <small class="text-muted">天数</small>
            </div>
        </div>
    </div>
</div>

<!-- 能力雷达图和进度图表 -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-radar-chart me-2"></i>能力雷达图
                </h6>
            </div>
            <div class="card-body">
                <canvas id="abilityRadarChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>学习进度趋势
                </h6>
            </div>
            <div class="card-body">
                <canvas id="progressChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 详细分析 -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>最近练习记录
                </h6>
            </div>
            <div class="card-body">
                {% if recent_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>练习时间</th>
                                <th>句子来源</th>
                                <th>结构得分</th>
                                <th>翻译得分</th>
                                <th>理解得分</th>
                                <th>综合得分</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>{{ record.created_at[:10] if record.created_at else 'N/A' }}</td>
                                <td>
                                    <a href="/analysis/sentence/{{ record.sentence_id }}" class="text-decoration-none">
                                        句子 #{{ record.sentence_id }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if record.structure_score >= 80 else 'warning' if record.structure_score >= 60 else 'danger' }}">
                                        {{ record.structure_score|round(1) if record.structure_score else 0 }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if record.translation_score >= 80 else 'warning' if record.translation_score >= 60 else 'danger' }}">
                                        {{ record.translation_score|round(1) if record.translation_score else 0 }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if record.comprehension_score >= 80 else 'warning' if record.comprehension_score >= 60 else 'danger' }}">
                                        {{ record.comprehension_score|round(1) if record.comprehension_score else 0 }}
                                    </span>
                                </td>
                                <td>
                                    <strong class="text-{{ 'success' if record.total_score >= 80 else 'warning' if record.total_score >= 60 else 'danger' }}">
                                        {{ record.total_score|round(1) if record.total_score else 0 }}
                                    </strong>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>还没有练习记录</p>
                    <a href="{{ url_for('practice.daily_practice') }}" class="btn btn-primary">
                        <i class="fas fa-play me-1"></i>开始练习
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>需要加强的方面
                </h6>
            </div>
            <div class="card-body">
                {% if user_stats.weak_points %}
                <div class="weak-points-list">
                    {% for point, count in user_stats.weak_points %}
                    <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
                        <div>
                            <strong>{{ point }}</strong>
                            <br>
                            <small class="text-muted">出现 {{ count }} 次</small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="practiceWeakPoint('{{ point }}')">
                                <i class="fas fa-dumbbell me-1"></i>练习
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p>表现优秀！<br>没有明显的薄弱点</p>
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <h6 class="text-muted">学习建议</h6>
                    <div class="suggestions">
                        {% if user_stats.average_score < 60 %}
                        <div class="alert alert-warning">
                            <small>
                                <i class="fas fa-lightbulb me-1"></i>
                                建议加强基础练习，重点关注句子结构分析
                            </small>
                        </div>
                        {% elif user_stats.average_score < 80 %}
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-thumbs-up me-1"></i>
                                表现不错！继续保持，可以尝试更难的句子
                            </small>
                        </div>
                        {% else %}
                        <div class="alert alert-success">
                            <small>
                                <i class="fas fa-star me-1"></i>
                                优秀！可以挑战最高难度的句子了
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成就系统 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">
                    <i class="fas fa-medal me-2"></i>学习成就
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="achievement-item text-center p-3 {{ 'achieved' if user_stats.total_sentences >= 10 else 'locked' }}">
                            <i class="fas fa-baby fa-2x mb-2 {{ 'text-warning' if user_stats.total_sentences >= 10 else 'text-muted' }}"></i>
                            <h6>初学者</h6>
                            <small>完成10个句子练习</small>
                            <div class="progress mt-2">
                                <div class="progress-bar" style="width: {{ min(100, (user_stats.total_sentences / 10) * 100) }}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="achievement-item text-center p-3 {{ 'achieved' if user_stats.total_sentences >= 50 else 'locked' }}">
                            <i class="fas fa-user-graduate fa-2x mb-2 {{ 'text-primary' if user_stats.total_sentences >= 50 else 'text-muted' }}"></i>
                            <h6>勤奋学者</h6>
                            <small>完成50个句子练习</small>
                            <div class="progress mt-2">
                                <div class="progress-bar" style="width: {{ min(100, (user_stats.total_sentences / 50) * 100) }}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="achievement-item text-center p-3 {{ 'achieved' if user_stats.average_score >= 80 else 'locked' }}">
                            <i class="fas fa-crown fa-2x mb-2 {{ 'text-warning' if user_stats.average_score >= 80 else 'text-muted' }}"></i>
                            <h6>语法大师</h6>
                            <small>平均分达到80分</small>
                            <div class="progress mt-2">
                                <div class="progress-bar" style="width: {{ min(100, user_stats.average_score) }}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="achievement-item text-center p-3 {{ 'achieved' if user_stats.total_time >= 3600 else 'locked' }}">
                            <i class="fas fa-clock fa-2x mb-2 {{ 'text-info' if user_stats.total_time >= 3600 else 'text-muted' }}"></i>
                            <h6>时间管理者</h6>
                            <small>累计学习1小时</small>
                            <div class="progress mt-2">
                                <div class="progress-bar" style="width: {{ min(100, (user_stats.total_time / 3600) * 100) }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.achievement-item.achieved {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #28a745;
    border-radius: 10px;
}

.achievement-item.locked {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    opacity: 0.6;
}

.stats-card:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化图表
    initAbilityRadarChart();
    initProgressChart();
});

// 初始化能力雷达图
function initAbilityRadarChart() {
    const ctx = document.getElementById('abilityRadarChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['结构分析', '词汇理解', '翻译能力', '逻辑推理', '语法掌握', '阅读速度'],
            datasets: [{
                label: '当前能力',
                data: [
                    {{ user_stats.structure_accuracy }},
                    75, // 词汇理解（示例数据）
                    {{ user_stats.translation_accuracy }},
                    {{ user_stats.comprehension_accuracy }},
                    80, // 语法掌握（示例数据）
                    70  // 阅读速度（示例数据）
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// 初始化进度图表
function initProgressChart() {
    const ctx = document.getElementById('progressChart').getContext('2d');
    
    // 模拟最近7天的数据
    const labels = [];
    const data = [];
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
        data.push(Math.floor(Math.random() * 30) + 60); // 模拟数据
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '每日平均分',
                data: data,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 0,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// 针对薄弱点进行练习
function practiceWeakPoint(weakPoint) {
    // 根据薄弱点跳转到相应的练习
    window.location.href = `/practice/daily?focus=${encodeURIComponent(weakPoint)}`;
}
</script>
{% endblock %}
