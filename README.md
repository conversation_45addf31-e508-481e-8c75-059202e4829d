# 考研英语长难句智能精练系统

## 项目简介
基于AI技术的考研英语长难句学习系统，提供每日精练、深度解析、智能巩固等功能。

## 功能特性
- 🎯 **智能语料库**：精选考研真题风格长难句
- 📚 **每日精练**：结构拆解、翻译练习、理解验证
- 🔍 **深度解析**：语法结构可视化、词汇详解
- 📊 **进度追踪**：学习记录、能力评估、数据可视化

## 技术栈
- **后端**：Python Flask
- **前端**：HTML5 + CSS3 + JavaScript
- **数据库**：SQLite
- **AI分析**：基础NLP处理

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python app.py
```

访问 http://localhost:5000 开始使用

## 项目结构
```
English-daily-learning/
├── app.py                 # Flask主应用
├── requirements.txt       # Python依赖
├── config.py             # 配置文件
├── models/               # 数据模型
├── routes/               # 路由处理
├── services/             # 业务逻辑
├── static/               # 静态资源
│   ├── css/
│   ├── js/
│   └── images/
├── templates/            # HTML模板
└── data/                 # 数据文件
    └── sentences.json    # 句子数据
```

## 开发计划
- [x] 项目架构设计与初始化
- [x] 语料库模块实现
- [x] 每日精练功能开发
- [x] 解析展示系统
- [x] 用户进度追踪

## 功能演示

### 1. 首页
- 显示学习统计概览
- 快速访问各个功能模块
- 个性化学习建议

### 2. 每日练习
- 精选3个长难句进行练习
- 三步练习流程：结构拆解 → 翻译练习 → 理解验证
- 实时反馈和评分

### 3. 句子解析库
- 按难度、话题、语法结构筛选句子
- 深度解析：语法结构、词汇详解、翻译指导
- 可视化展示句子结构

### 4. 学习统计
- 能力雷达图显示各项能力
- 学习进度趋势图
- 薄弱点分析和针对性建议
- 成就系统激励学习

## 系统特色

### 🎯 智能语料库
- 基于考研真题风格的长难句
- 多维度难度评估
- 语法结构标注

### 📚 三步练习法
1. **结构拆解**：识别句子主干和修饰成分
2. **翻译练习**：英译中能力训练
3. **理解验证**：深层理解检验

### 🔍 深度解析
- 语法结构可视化
- 重点词汇详解
- 翻译技巧指导

### 📊 智能追踪
- 学习进度可视化
- 错误模式分析
- 个性化改进建议

## 技术实现

### 后端架构
- **Flask**: Web框架
- **SQLAlchemy**: 数据库ORM
- **SQLite**: 轻量级数据库

### 前端技术
- **Bootstrap 5**: 响应式UI框架
- **Chart.js**: 数据可视化
- **jQuery**: JavaScript库

### 数据模型
- **Sentence**: 句子模型（内容、难度、语法标注等）
- **UserProgress**: 用户进度模型（练习记录、得分、错误分析等）

## 使用指南

### 开始学习
1. 访问首页查看学习概览
2. 点击"开始今日练习"进入每日练习
3. 按照三步流程完成练习
4. 查看详细反馈和解析

### 查看解析
1. 进入"句子解析库"
2. 使用筛选功能找到目标句子
3. 点击"详细解析"查看深度分析
4. 学习语法结构和词汇要点

### 追踪进度
1. 访问"学习统计"页面
2. 查看能力雷达图和进度趋势
3. 关注薄弱点分析
4. 根据建议调整学习策略
