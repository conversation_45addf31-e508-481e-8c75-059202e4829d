// 主要JavaScript功能文件

$(document).ready(function() {
    // 初始化工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // 初始化弹出框
    $('[data-bs-toggle="popover"]').popover();
    
    // 平滑滚动
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // 添加页面加载动画
    $('.card, .alert').addClass('fade-in-up');
});

// 练习相关功能
class PracticeManager {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 3;
        this.progressId = null;
        this.scores = {
            structure: 0,
            translation: 0,
            comprehension: 0
        };
    }
    
    // 开始练习
    startPractice(sentenceId) {
        const data = {
            sentence_id: sentenceId
        };
        
        $.ajax({
            url: '/practice/api/start-practice',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: (response) => {
                if (response.success) {
                    this.progressId = response.progress_id;
                    this.showStep(1);
                } else {
                    this.showError(response.message);
                }
            },
            error: () => {
                this.showError('网络错误，请稍后重试');
            }
        });
    }
    
    // 提交结构拆解答案
    submitStructure(userStructure) {
        if (!userStructure.trim()) {
            this.showError('请输入结构拆解答案');
            return;
        }
        
        this.showLoading('structure-submit-btn');
        
        const data = {
            progress_id: this.progressId,
            user_structure: userStructure
        };
        
        $.ajax({
            url: '/practice/api/submit-structure',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: (response) => {
                this.hideLoading('structure-submit-btn');
                if (response.success) {
                    this.scores.structure = response.score;
                    this.showFeedback('structure', response.score, response.feedback);
                    this.completeStep(1);
                    setTimeout(() => this.showStep(2), 2000);
                } else {
                    this.showError(response.message);
                }
            },
            error: () => {
                this.hideLoading('structure-submit-btn');
                this.showError('提交失败，请稍后重试');
            }
        });
    }
    
    // 提交翻译答案
    submitTranslation(userTranslation) {
        if (!userTranslation.trim()) {
            this.showError('请输入翻译答案');
            return;
        }
        
        this.showLoading('translation-submit-btn');
        
        const data = {
            progress_id: this.progressId,
            user_translation: userTranslation
        };
        
        $.ajax({
            url: '/practice/api/submit-translation',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: (response) => {
                this.hideLoading('translation-submit-btn');
                if (response.success) {
                    this.scores.translation = response.score;
                    this.showFeedback('translation', response.score, response.feedback);
                    this.showReferenceTranslation(response.reference_translation);
                    this.completeStep(2);
                    setTimeout(() => this.showStep(3), 2000);
                } else {
                    this.showError(response.message);
                }
            },
            error: () => {
                this.hideLoading('translation-submit-btn');
                this.showError('提交失败，请稍后重试');
            }
        });
    }
    
    // 提交理解验证答案
    submitComprehension(userAnswer) {
        if (userAnswer === null || userAnswer === undefined) {
            this.showError('请选择答案');
            return;
        }
        
        this.showLoading('comprehension-submit-btn');
        
        const data = {
            progress_id: this.progressId,
            user_answer: userAnswer
        };
        
        $.ajax({
            url: '/practice/api/submit-comprehension',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: (response) => {
                this.hideLoading('comprehension-submit-btn');
                if (response.success) {
                    this.scores.comprehension = response.score;
                    this.showFeedback('comprehension', response.score, response.explanation);
                    this.completeStep(3);
                    this.showFinalResults(response.total_score, response.error_analysis);
                } else {
                    this.showError(response.message);
                }
            },
            error: () => {
                this.hideLoading('comprehension-submit-btn');
                this.showError('提交失败，请稍后重试');
            }
        });
    }
    
    // 显示步骤
    showStep(stepNumber) {
        $('.practice-step').removeClass('active');
        $(`.practice-step[data-step="${stepNumber}"]`).addClass('active');
        this.currentStep = stepNumber;
        this.updateProgress();
    }
    
    // 完成步骤
    completeStep(stepNumber) {
        $(`.practice-step[data-step="${stepNumber}"]`).addClass('completed');
        $(`.practice-step[data-step="${stepNumber}"] .step-number`).addClass('completed');
        this.updateProgress();
    }
    
    // 更新进度条
    updateProgress() {
        const progress = (this.currentStep - 1) / this.totalSteps * 100;
        $('.progress-bar').css('width', `${progress}%`);
    }
    
    // 显示反馈
    showFeedback(type, score, feedback) {
        let feedbackClass = 'feedback-poor';
        if (score >= 80) feedbackClass = 'feedback-excellent';
        else if (score >= 60) feedbackClass = 'feedback-good';
        else if (score >= 40) feedbackClass = 'feedback-needs-improvement';
        
        const feedbackHtml = `
            <div class="feedback-container ${feedbackClass}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <strong>得分: ${score.toFixed(1)}分</strong>
                    <span class="badge bg-primary">${this.getScoreLevel(score)}</span>
                </div>
                <p class="mb-0">${feedback}</p>
            </div>
        `;
        
        $(`#${type}-feedback`).html(feedbackHtml).show();
    }
    
    // 显示参考翻译
    showReferenceTranslation(translation) {
        const translationHtml = `
            <div class="mt-3 p-3 bg-light rounded">
                <h6 class="text-muted mb-2">参考翻译：</h6>
                <p class="mb-0">${translation}</p>
            </div>
        `;
        $('#translation-reference').html(translationHtml).show();
    }
    
    // 显示最终结果
    showFinalResults(totalScore, errorAnalysis) {
        const resultsHtml = `
            <div class="card mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>练习完成！
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-primary">${this.scores.structure.toFixed(1)}</div>
                                <div class="stats-label">结构拆解</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">${this.scores.translation.toFixed(1)}</div>
                                <div class="stats-label">翻译练习</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">${this.scores.comprehension.toFixed(1)}</div>
                                <div class="stats-label">理解验证</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-info">${totalScore.toFixed(1)}</div>
                                <div class="stats-label">综合得分</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="/practice/daily" class="btn btn-primary me-2">
                            <i class="fas fa-redo me-1"></i>继续练习
                        </a>
                        <a href="/dashboard" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line me-1"></i>查看统计
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        $('#final-results').html(resultsHtml).show();
    }
    
    // 获取分数等级
    getScoreLevel(score) {
        if (score >= 90) return '优秀';
        if (score >= 80) return '良好';
        if (score >= 70) return '中等';
        if (score >= 60) return '及格';
        return '需要加强';
    }
    
    // 显示加载状态
    showLoading(buttonId) {
        const $btn = $(`#${buttonId}`);
        $btn.prop('disabled', true);
        $btn.html('<span class="loading-spinner me-2"></span>提交中...');
    }
    
    // 隐藏加载状态
    hideLoading(buttonId) {
        const $btn = $(`#${buttonId}`);
        $btn.prop('disabled', false);
        $btn.html($btn.data('original-text') || '提交答案');
    }
    
    // 显示错误信息
    showError(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('#error-container').html(alertHtml);
    }
}

// 全局练习管理器实例
window.practiceManager = new PracticeManager();

// 工具函数
const Utils = {
    // 格式化时间
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    
    // 复制到剪贴板
    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showToast('已复制到剪贴板', 'success');
        }).catch(() => {
            this.showToast('复制失败', 'error');
        });
    },
    
    // 显示提示消息
    showToast(message, type = 'info') {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        const $toast = $(toastHtml);
        $('#toast-container').append($toast);
        
        const toast = new bootstrap.Toast($toast[0]);
        toast.show();
        
        // 自动移除
        $toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
};

// 导出到全局
window.Utils = Utils;
