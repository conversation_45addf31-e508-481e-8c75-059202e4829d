from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import os
from config import config
from database import db

# 创建Flask应用
app = Flask(__name__)

# 加载配置
config_name = os.environ.get('FLASK_CONFIG', 'default')
app.config.from_object(config[config_name])

# 初始化扩展
db.init_app(app)
CORS(app)

# 导入路由
from routes.main import main_bp
from routes.practice import practice_bp
from routes.analysis import analysis_bp

# 注册蓝图
app.register_blueprint(main_bp)
app.register_blueprint(practice_bp, url_prefix='/practice')
app.register_blueprint(analysis_bp, url_prefix='/analysis')

def create_tables():
    """创建数据库表"""
    with app.app_context():
        # 导入模型（在app context中）
        from models.sentence import Sentence
        from models.user_progress import UserProgress

        db.create_all()

        # 初始化示例数据
        from services.data_service import DataService
        data_service = DataService()
        data_service.initialize_sample_data()

@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('500.html'), 500

if __name__ == '__main__':
    # 创建数据库表
    create_tables()
    app.run(debug=app.config['DEBUG'], host='0.0.0.0', port=5000)
