{% extends "base.html" %}

{% block title %}句子解析库 - 考研英语长难句智能精练系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2>
            <i class="fas fa-search text-primary me-2"></i>
            句子解析库
        </h2>
        <p class="text-muted">深度解析考研英语长难句，掌握语法结构和词汇要点</p>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="difficulty-filter" class="form-label">难度等级</label>
                        <select class="form-select" id="difficulty-filter">
                            <option value="">全部难度</option>
                            <option value="1">1级 - 基础</option>
                            <option value="2">2级 - 简单</option>
                            <option value="3">3级 - 中等</option>
                            <option value="4">4级 - 困难</option>
                            <option value="5">5级 - 极难</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="topic-filter" class="form-label">话题分类</label>
                        <select class="form-select" id="topic-filter">
                            <option value="">全部话题</option>
                            <option value="科学技术">科学技术</option>
                            <option value="社会科学">社会科学</option>
                            <option value="经济管理">经济管理</option>
                            <option value="文化教育">文化教育</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="grammar-filter" class="form-label">语法结构</label>
                        <select class="form-select" id="grammar-filter">
                            <option value="">全部语法</option>
                            <option value="定语从句">定语从句</option>
                            <option value="状语从句">状语从句</option>
                            <option value="非谓语动词">非谓语动词</option>
                            <option value="被动语态">被动语态</option>
                            <option value="虚拟语气">虚拟语气</option>
                        </select>
                    </div>
                </div>
                <div class="text-center">
                    <button type="button" class="btn btn-primary" onclick="searchSentences()">
                        <i class="fas fa-search me-2"></i>搜索句子
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-undo me-2"></i>重置筛选
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 句子列表 -->
<div class="row" id="sentences-container">
    <div class="col-12 text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载句子...</p>
    </div>
</div>

<!-- 分页 -->
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="句子分页">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let totalPages = 1;

$(document).ready(function() {
    loadSentences();
});

// 加载句子列表
function loadSentences(page = 1) {
    const difficulty = $('#difficulty-filter').val();
    const topic = $('#topic-filter').val();
    const grammar = $('#grammar-filter').val();
    
    const params = new URLSearchParams();
    if (difficulty) params.append('difficulty', difficulty);
    if (topic) params.append('topic', topic);
    if (grammar) params.append('grammar_tag', grammar);
    params.append('limit', '9'); // 每页9个
    
    $.ajax({
        url: `/api/sentences?${params.toString()}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displaySentences(response.data);
                updatePagination(response.count);
            } else {
                showError('加载句子失败');
            }
        },
        error: function() {
            showError('网络错误，请稍后重试');
        }
    });
}

// 显示句子列表
function displaySentences(sentences) {
    if (sentences.length === 0) {
        $('#sentences-container').html(`
            <div class="col-12 text-center">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">没有找到符合条件的句子</h5>
                <p class="text-muted">请尝试调整筛选条件</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    sentences.forEach(sentence => {
        html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 sentence-card" data-sentence-id="${sentence.id}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span class="badge bg-primary">难度 ${sentence.difficulty_level}/5</span>
                        <small class="text-muted">${sentence.source || '未知来源'}</small>
                    </div>
                    <div class="card-body">
                        <div class="sentence-preview mb-3">
                            <p class="card-text small">${sentence.content.substring(0, 120)}${sentence.content.length > 120 ? '...' : ''}</p>
                        </div>
                        
                        <div class="sentence-info mb-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <small class="text-muted">词数</small>
                                    <div class="fw-bold">${sentence.word_count}</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">话题</small>
                                    <div class="fw-bold small">${sentence.topic || '通用'}</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">语法点</small>
                                    <div class="fw-bold">${sentence.grammar_tags.length}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grammar-tags mb-3">
                            ${sentence.grammar_tags.map(tag => `<span class="badge bg-secondary me-1 small">${tag}</span>`).join('')}
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-grid gap-2">
                            <a href="/analysis/sentence/${sentence.id}" class="btn btn-primary btn-sm">
                                <i class="fas fa-microscope me-1"></i>详细解析
                            </a>
                            <a href="/practice/sentence/${sentence.id}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-dumbbell me-1"></i>开始练习
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#sentences-container').html(html);
    
    // 添加卡片点击效果
    $('.sentence-card').click(function(e) {
        if (!$(e.target).is('a, button')) {
            const sentenceId = $(this).data('sentence-id');
            window.location.href = `/analysis/sentence/${sentenceId}`;
        }
    });
}

// 更新分页
function updatePagination(totalCount) {
    const itemsPerPage = 9;
    totalPages = Math.ceil(totalCount / itemsPerPage);
    
    if (totalPages <= 1) {
        $('#pagination').empty();
        return;
    }
    
    let paginationHtml = '';
    
    // 上一页
    if (currentPage > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
            </li>
        `;
    }
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
            paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
        }
    }
    
    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
            </li>
        `;
    }
    
    $('#pagination').html(paginationHtml);
}

// 切换页面
function changePage(page) {
    currentPage = page;
    loadSentences(page);
    $('html, body').animate({scrollTop: 0}, 500);
}

// 搜索句子
function searchSentences() {
    currentPage = 1;
    loadSentences();
}

// 重置筛选
function resetFilters() {
    $('#difficulty-filter').val('');
    $('#topic-filter').val('');
    $('#grammar-filter').val('');
    currentPage = 1;
    loadSentences();
}

// 显示错误信息
function showError(message) {
    $('#sentences-container').html(`
        <div class="col-12">
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        </div>
    `);
}
</script>
{% endblock %}
