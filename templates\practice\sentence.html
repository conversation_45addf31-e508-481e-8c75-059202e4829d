{% extends "base.html" %}

{% block title %}句子练习 - {{ sentence.source }} - 考研英语长难句智能精练系统{% endblock %}

{% block content %}
<!-- 错误提示容器 -->
<div id="error-container"></div>

<!-- 句子信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-book-open text-primary me-2"></i>
                        {{ sentence.source }}
                    </h5>
                </div>
                <div>
                    <span class="badge bg-primary me-2">难度 {{ sentence.difficulty_level }}/5</span>
                    <span class="badge bg-secondary">{{ sentence.topic }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="sentence-text">
                    {{ sentence.content }}
                </div>
                <div class="row text-center">
                    <div class="col-md-3">
                        <small class="text-muted">词数</small>
                        <div class="fw-bold">{{ sentence.word_count }}</div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">语法点</small>
                        <div class="fw-bold">{{ sentence.grammar_tags|length }}</div>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">主要语法结构</small>
                        <div>
                            {% for tag in sentence.grammar_tags %}
                            <span class="badge bg-outline-secondary me-1">{{ tag }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 进度条 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="progress" style="height: 10px;">
            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
        </div>
        <div class="d-flex justify-content-between mt-2">
            <small class="text-muted">结构拆解</small>
            <small class="text-muted">翻译练习</small>
            <small class="text-muted">理解验证</small>
        </div>
    </div>
</div>

<!-- 第一步：结构拆解 -->
<div class="practice-step" data-step="1">
    <div class="step-header">
        <div class="step-number">1</div>
        <div>
            <h4>结构拆解</h4>
            <p class="text-muted mb-0">请识别句子的主干结构，标注主要成分</p>
        </div>
    </div>
    
    <div class="mb-3">
        <label for="structure-input" class="form-label">请分析句子结构（标注主语、谓语、宾语等成分）：</label>
        <textarea class="form-control" id="structure-input" rows="4" 
                  placeholder="例如：主语：The theory；谓语：has been supported；宾语：by numerous observations..."></textarea>
    </div>
    
    <div class="d-grid">
        <button type="button" class="btn btn-primary" id="structure-submit-btn" 
                onclick="practiceManager.submitStructure($('#structure-input').val())">
            <i class="fas fa-check me-2"></i>提交答案
        </button>
    </div>
    
    <div id="structure-feedback" class="mt-3" style="display: none;"></div>
</div>

<!-- 第二步：翻译练习 -->
<div class="practice-step" data-step="2" style="display: none;">
    <div class="step-header">
        <div class="step-number">2</div>
        <div>
            <h4>翻译练习</h4>
            <p class="text-muted mb-0">请将句子翻译成通顺的中文</p>
        </div>
    </div>
    
    <div class="mb-3">
        <label for="translation-input" class="form-label">请翻译这个句子：</label>
        <textarea class="form-control" id="translation-input" rows="4" 
                  placeholder="请输入您的翻译..."></textarea>
    </div>
    
    <div class="d-grid">
        <button type="button" class="btn btn-success" id="translation-submit-btn"
                onclick="practiceManager.submitTranslation($('#translation-input').val())">
            <i class="fas fa-language me-2"></i>提交翻译
        </button>
    </div>
    
    <div id="translation-feedback" class="mt-3" style="display: none;"></div>
    <div id="translation-reference" class="mt-3" style="display: none;"></div>
</div>

<!-- 第三步：理解验证 -->
<div class="practice-step" data-step="3" style="display: none;">
    <div class="step-header">
        <div class="step-number">3</div>
        <div>
            <h4>理解验证</h4>
            <p class="text-muted mb-0">请选择正确答案验证您的理解</p>
        </div>
    </div>
    
    <div id="comprehension-question" class="mb-3">
        <!-- 题目将通过JavaScript动态加载 -->
    </div>
    
    <div class="d-grid">
        <button type="button" class="btn btn-warning" id="comprehension-submit-btn" style="display: none;"
                onclick="practiceManager.submitComprehension($('input[name=comprehension]:checked').val())">
            <i class="fas fa-lightbulb me-2"></i>提交答案
        </button>
    </div>
    
    <div id="comprehension-feedback" class="mt-3" style="display: none;"></div>
</div>

<!-- 最终结果 -->
<div id="final-results" style="display: none;"></div>

<!-- 操作按钮 -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="{{ url_for('practice.daily_practice') }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-arrow-left me-1"></i>返回练习列表
        </a>
        <a href="{{ url_for('analysis.analyze_sentence', sentence_id=sentence.id) }}" class="btn btn-outline-info">
            <i class="fas fa-search me-1"></i>查看详细解析
        </a>
    </div>
</div>

<!-- Toast容器 -->
<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 自动开始练习
    practiceManager.startPractice({{ sentence.id }});
    
    // 保存按钮原始文本
    $('#structure-submit-btn').data('original-text', '提交答案');
    $('#translation-submit-btn').data('original-text', '提交翻译');
    $('#comprehension-submit-btn').data('original-text', '提交答案');
    
    // 监听步骤变化
    $(document).on('stepChanged', function(e, stepNumber) {
        $('.practice-step').hide();
        $(`.practice-step[data-step="${stepNumber}"]`).show();
        
        // 如果是第三步，生成理解题
        if (stepNumber === 3) {
            generateComprehensionQuestion();
        }
    });
    
    // 键盘快捷键
    $(document).keydown(function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            const currentStep = practiceManager.currentStep;
            if (currentStep === 1) {
                $('#structure-submit-btn').click();
            } else if (currentStep === 2) {
                $('#translation-submit-btn').click();
            } else if (currentStep === 3) {
                $('#comprehension-submit-btn').click();
            }
        }
    });
});

// 生成理解验证题目
function generateComprehensionQuestion() {
    const questionHtml = `
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">句子中体现的主要逻辑关系是？</h6>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="comprehension" id="option1" value="0">
                    <label class="form-check-label" for="option1">因果关系</label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="comprehension" id="option2" value="1">
                    <label class="form-check-label" for="option2">转折关系</label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="comprehension" id="option3" value="2">
                    <label class="form-check-label" for="option3">递进关系</label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio" name="comprehension" id="option4" value="3">
                    <label class="form-check-label" for="option4">并列关系</label>
                </div>
            </div>
        </div>
    `;
    
    $('#comprehension-question').html(questionHtml);
    
    // 监听选择变化
    $('input[name=comprehension]').change(function() {
        $('#comprehension-submit-btn').show();
    });
}

// 重写练习管理器的showStep方法
practiceManager.showStep = function(stepNumber) {
    $('.practice-step').removeClass('active').hide();
    $(`.practice-step[data-step="${stepNumber}"]`).addClass('active').show();
    this.currentStep = stepNumber;
    this.updateProgress();
    
    // 触发步骤变化事件
    $(document).trigger('stepChanged', [stepNumber]);
};
</script>
{% endblock %}
