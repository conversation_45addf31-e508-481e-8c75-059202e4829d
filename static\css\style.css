/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-lg {
    padding: 0.75rem 2rem;
}

/* 徽章样式 */
.badge {
    border-radius: 15px;
    font-weight: 500;
}

/* 句子练习相关样式 */
.sentence-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.sentence-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

/* 语法高亮样式 */
.main-clause {
    background-color: #e3f2fd;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.subordinate-clause {
    background-color: #fff3e0;
    padding: 2px 4px;
    border-radius: 3px;
    font-style: italic;
}

.key-vocabulary {
    background-color: #f3e5f5;
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.key-vocabulary:hover {
    background-color: #e1bee7;
}

/* 练习步骤样式 */
.practice-step {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.practice-step.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.practice-step.completed {
    border-color: var(--success-color);
    background-color: #f8fff9;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.step-number.completed {
    background: var(--success-color);
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* 反馈样式 */
.feedback-container {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid;
}

.feedback-excellent {
    background-color: #d4edda;
    border-left-color: var(--success-color);
    color: #155724;
}

.feedback-good {
    background-color: #cce7ff;
    border-left-color: var(--info-color);
    color: #0c5460;
}

.feedback-needs-improvement {
    background-color: #fff3cd;
    border-left-color: var(--warning-color);
    color: #856404;
}

.feedback-poor {
    background-color: #f8d7da;
    border-left-color: var(--danger-color);
    color: #721c24;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* 统计图表样式 */
.stats-card {
    text-align: center;
    padding: 1.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sentence-text {
        font-size: 1rem;
        padding: 1rem;
    }
    
    .practice-step {
        padding: 1rem;
    }
    
    .step-header {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #333;
    color: white;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 0.875rem;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
