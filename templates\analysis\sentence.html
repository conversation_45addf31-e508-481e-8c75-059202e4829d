{% extends "base.html" %}

{% block title %}句子解析 - {{ sentence.source }} - 考研英语长难句智能精练系统{% endblock %}

{% block content %}
<!-- 句子基本信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-microscope text-primary me-2"></i>
                    句子详细解析
                </h5>
                <div>
                    <span class="badge bg-primary me-2">难度 {{ sentence.difficulty_level }}/5</span>
                    <span class="badge bg-secondary">{{ sentence.topic }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="sentence-text mb-4" id="sentence-display">
                    {{ sentence.content }}
                </div>
                
                <div class="row text-center">
                    <div class="col-md-2">
                        <small class="text-muted">来源</small>
                        <div class="fw-bold">{{ sentence.source }}</div>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">词数</small>
                        <div class="fw-bold">{{ sentence.word_count }}</div>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">难度分数</small>
                        <div class="fw-bold">{{ sentence.flesch_score|round(1) if sentence.flesch_score else 'N/A' }}</div>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">语法标签</small>
                        <div>
                            {% for tag in sentence.grammar_tags %}
                            <span class="badge bg-outline-primary me-1">{{ tag }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 解析内容 -->
<div class="row">
    <!-- 结构分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-sitemap me-2"></i>结构分析
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">主句结构</h6>
                    <div class="p-3 bg-light rounded">
                        {{ sentence.main_clause or '主句结构分析中...' }}
                    </div>
                </div>
                
                {% if sentence.subordinate_clauses %}
                <div class="mb-3">
                    <h6 class="text-success">从句分析</h6>
                    {% for clause in sentence.subordinate_clauses %}
                    <div class="mb-2 p-2 border-start border-success border-3 bg-light">
                        <strong>{{ clause.type }}：</strong>{{ clause.content }}<br>
                        <small class="text-muted">功能：{{ clause.function }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <div>
                    <h6 class="text-info">关键结构</h6>
                    <div class="p-2 bg-info bg-opacity-10 rounded">
                        {{ sentence.key_structures or '结构特点分析中...' }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 词汇解析 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-book me-2"></i>词汇解析
                </h6>
            </div>
            <div class="card-body">
                {% if sentence.key_vocabulary %}
                {% for vocab in sentence.key_vocabulary %}
                <div class="vocab-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="text-primary mb-0">{{ vocab.word }}</h6>
                        <span class="badge bg-secondary">{{ vocab.type }}</span>
                    </div>
                    <p class="mb-1"><strong>释义：</strong>{{ vocab.meaning }}</p>
                    <button class="btn btn-sm btn-outline-info" onclick="showVocabDetails('{{ vocab.word }}')">
                        <i class="fas fa-info-circle me-1"></i>详细信息
                    </button>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted">词汇解析加载中...</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 语法解析 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>语法解析
                </h6>
            </div>
            <div class="card-body">
                {% for tag in sentence.grammar_tags %}
                <div class="grammar-item mb-3">
                    <h6 class="text-warning">{{ tag }}</h6>
                    <button class="btn btn-sm btn-outline-warning" onclick="showGrammarExplanation('{{ tag }}')">
                        <i class="fas fa-question-circle me-1"></i>查看解释
                    </button>
                </div>
                {% endfor %}
                
                <div id="grammar-explanation" class="mt-3" style="display: none;">
                    <!-- 语法解释将通过JavaScript加载 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 翻译指导 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-language me-2"></i>翻译指导
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-info">参考翻译</h6>
                    <div class="p-3 bg-light rounded">
                        {{ sentence.translation or '翻译加载中...' }}
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-secondary">翻译要点</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>注意句子的逻辑关系</li>
                        <li><i class="fas fa-check text-success me-2"></i>准确理解关键词汇</li>
                        <li><i class="fas fa-check text-success me-2"></i>符合中文表达习惯</li>
                    </ul>
                </div>
                
                <button class="btn btn-outline-info" onclick="showTranslationGuide()">
                    <i class="fas fa-lightbulb me-1"></i>查看详细指导
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 综合解析 -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>综合解析
                </h6>
            </div>
            <div class="card-body">
                <div class="analysis-content">
                    {{ sentence.analysis or '综合解析加载中...' }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="row">
    <div class="col-12 text-center">
        <a href="{{ url_for('practice.practice_sentence', sentence_id=sentence.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-dumbbell me-1"></i>开始练习
        </a>
        <a href="{{ url_for('analysis.analysis_home') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i>返回解析库
        </a>
        <button class="btn btn-outline-info" onclick="copyToClipboard('{{ sentence.content }}')">
            <i class="fas fa-copy me-1"></i>复制句子
        </button>
    </div>
</div>

<!-- 模态框 - 词汇详情 -->
<div class="modal fade" id="vocabModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">词汇详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="vocab-details">
                <!-- 词汇详情将通过JavaScript加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 模态框 - 语法解释 -->
<div class="modal fade" id="grammarModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">语法解释</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="grammar-details">
                <!-- 语法解释将通过JavaScript加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 显示词汇详情
function showVocabDetails(word) {
    $.ajax({
        url: `/analysis/api/vocabulary/${word}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const vocab = response.data;
                const html = `
                    <div class="vocab-details">
                        <h6 class="text-primary">${word}</h6>
                        <p><strong>发音：</strong>${vocab.pronunciation || '暂无'}</p>
                        <p><strong>词性：</strong>${vocab.part_of_speech || '暂无'}</p>
                        <p><strong>释义：</strong>${vocab.meaning || '暂无'}</p>
                        ${vocab.synonyms && vocab.synonyms.length > 0 ? `<p><strong>同义词：</strong>${vocab.synonyms.join(', ')}</p>` : ''}
                        ${vocab.examples && vocab.examples.length > 0 ? `<div><strong>例句：</strong><ul>${vocab.examples.map(ex => `<li>${ex}</li>`).join('')}</ul></div>` : ''}
                    </div>
                `;
                $('#vocab-details').html(html);
                $('#vocabModal').modal('show');
            }
        },
        error: function() {
            $('#vocab-details').html('<p class="text-danger">加载词汇信息失败</p>');
            $('#vocabModal').modal('show');
        }
    });
}

// 显示语法解释
function showGrammarExplanation(grammarPoint) {
    $.ajax({
        url: `/analysis/api/grammar/${grammarPoint}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const grammar = response.data;
                const html = `
                    <div class="grammar-details">
                        <h6 class="text-warning">${grammarPoint}</h6>
                        <p><strong>定义：</strong>${grammar.definition || '暂无'}</p>
                        ${grammar.markers && grammar.markers.length > 0 ? `<p><strong>标志词：</strong>${grammar.markers.join(', ')}</p>` : ''}
                        ${grammar.examples && grammar.examples.length > 0 ? `<div><strong>例句：</strong><ul>${grammar.examples.map(ex => `<li>${ex}</li>`).join('')}</ul></div>` : ''}
                    </div>
                `;
                $('#grammar-details').html(html);
                $('#grammarModal').modal('show');
            }
        },
        error: function() {
            $('#grammar-details').html('<p class="text-danger">加载语法信息失败</p>');
            $('#grammarModal').modal('show');
        }
    });
}

// 显示翻译指导
function showTranslationGuide() {
    Utils.showToast('翻译指导功能开发中', 'info');
}

// 复制到剪贴板
function copyToClipboard(text) {
    Utils.copyToClipboard(text);
}

// 页面加载完成后的初始化
$(document).ready(function() {
    // 高亮显示句子结构（简化版本）
    highlightSentenceStructure();
});

// 高亮句子结构
function highlightSentenceStructure() {
    // 这里可以实现更复杂的结构高亮逻辑
    // 现在只是简单的示例
    const sentenceText = $('#sentence-display').text();
    // 可以根据语法标签进行高亮处理
}
</script>
{% endblock %}
